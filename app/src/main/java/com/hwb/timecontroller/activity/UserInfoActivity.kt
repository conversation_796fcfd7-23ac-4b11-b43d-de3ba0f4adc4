package com.hwb.timecontroller.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.CountDownTimer
import android.util.Base64
import android.view.View
import android.view.WindowInsets
import android.view.WindowInsetsController
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.Observer
import coil.load
import coil.transform.CircleCropTransformation
import com.elvishew.xlog.XLog
import com.hi.dhl.binding.viewbind
import com.hwb.timecontroller.R
import com.hwb.timecontroller.business.BalancePollingManager
import com.hwb.timecontroller.business.UserManager
import com.hwb.timecontroller.databinding.ActivityUserInfoBinding
import com.hwb.timecontroller.service.CountdownService
import com.hwb.timecontroller.viewModel.LoginViewModel
import com.hwb.timecontroller.viewModel.UserInfoViewModel
import com.kongzue.dialogx.dialogs.MessageDialog
import com.kongzue.dialogx.dialogs.CustomDialog
import android.os.Handler
import android.os.Looper
import android.widget.Button
import android.widget.TextView
import com.kongzue.dialogx.interfaces.OnBindView

/**
 * 用户信息页面
 *
 * Author: huangwubin
 * Date: 2025/7/11
 */
class UserInfoActivity : AppCompatActivity() {

    /**
     * 用户信息页面入口类型
     */
    enum class UserInfoEntryType {
        NORMAL_ENTRY,        // 正常入口（登录后等）
        COUNTDOWN_ENDED_ENTRY // 倒计时结束入口
    }

    companion object {
        private const val EXTRA_ENTRY_TYPE = "entry_type"

        /// 页面倒计时分钟
        private const val PAGE_COUNTDOWN_MINUTES = 3

        /**
         * 启动用户信息页面
         * @param context 上下文
         * @param entryType 入口类型，默认为正常入口
         */
        fun start(context: Context, entryType: UserInfoEntryType = UserInfoEntryType.NORMAL_ENTRY) {
            val intent = Intent(context, UserInfoActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
                putExtra(EXTRA_ENTRY_TYPE, entryType.name)
            }
            context.startActivity(intent)
        }
    }

    private val binding by viewbind<ActivityUserInfoBinding>()
    private val viewModel: UserInfoViewModel by viewModels()
    private val loginViewModel by viewModels<LoginViewModel>()

    // 入口类型
    private var entryType: UserInfoEntryType = UserInfoEntryType.NORMAL_ENTRY

    // 页面倒计时相关
    private var pageCountdown: CountDownTimer? = null
    private var isCountdownActive = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContentView(binding.root)

        // 设置全屏
        setupFullscreen()
        supportActionBar?.hide()

        setupClickListeners()
        setupObservers()

        initData(intent)

        binding.ivQrCode.post {
            //生成二维码
            loginViewModel.generateQRCodeDirectly()
        }
    }

    private fun initData(intent: Intent) {
        //获取进入类型
        extractEntryType(intent)
        // 获取用户数据
        extractUserData()
        // 主动刷新余额
        viewModel.refreshBalanceOnEnter()

        // 检查余额状态
        checkBalanceStatus()

        // 根据入口类型设置按钮文案
        updateButtonTextByEntryType()

        // 初始化时更新UI
        updateUserInfo()

        try {
            XLog.d("初始化状态")

            // 停止旧的页面倒计时
            stopPageCountdown()

            // 重新启动页面倒计时
            startPageCountdown()

        } catch (e: Exception) {
            XLog.e("Activity复用重新初始化失败", e)
        }
    }

    /**
     * 提取入口类型
     */
    private fun extractEntryType(intent: Intent) {
        val entryTypeName = intent.getStringExtra(EXTRA_ENTRY_TYPE)
        entryType = if (entryTypeName != null) {
            try {
                UserInfoEntryType.valueOf(entryTypeName)
            } catch (e: IllegalArgumentException) {
                XLog.w("无效的入口类型: $entryTypeName，使用默认值")
                UserInfoEntryType.NORMAL_ENTRY
            }
        } else {
            UserInfoEntryType.NORMAL_ENTRY
        }
        XLog.d("UserInfoActivity入口类型: $entryType")
    }

    /**
     * 设置全屏模式
     */
    private fun setupFullscreen() {
        // Android 11+ 使用新的WindowInsetsController API
        window.insetsController?.let { controller ->
            controller.hide(WindowInsets.Type.statusBars() or WindowInsets.Type.navigationBars())
            controller.systemBarsBehavior =
                WindowInsetsController.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
        }
    }

    /**
     * 提取用户数据
     */
    private fun extractUserData() {
        val userInfo = UserManager.getClientLoginData()
        if (userInfo == null) {
            XLog.w("未获取到用户数据，根据入口类型处理")
            // 根据入口类型处理退出逻辑
            handleExitByEntryType()
        } else {
            viewModel.setUserData(userInfo)
            XLog.d("获取到用户数据: ${userInfo.userName}")
        }
    }

    /**
     * 根据入口类型处理退出逻辑
     */
    private fun handleExitByEntryType() {
        when (entryType) {
            UserInfoEntryType.NORMAL_ENTRY -> {
                XLog.d("正常入口：退出页面")
                finish()
            }
            UserInfoEntryType.COUNTDOWN_ENDED_ENTRY -> {
                XLog.d("倒计时入口：切换到后台")
                moveTaskToBack(true)
            }
        }
    }

    /**
     * 根据入口类型更新按钮文案
     */
    private fun updateButtonTextByEntryType() {
        when (entryType) {
            UserInfoEntryType.NORMAL_ENTRY -> {
                binding.btnStartPlaying.text = "开始租借平板"
            }
            UserInfoEntryType.COUNTDOWN_ENDED_ENTRY -> {
                binding.btnStartPlaying.text = "继续"
            }
        }
        XLog.d("按钮文案已更新为: ${binding.btnStartPlaying.text}")
    }

    /**
     * 设置点击监听器
     */
    private fun setupClickListeners() {
        // 返回按钮（已隐藏，但保留逻辑）
        binding.btnBack.setOnClickListener {
            // 不执行任何操作，屏蔽返回功能
        }

        // 开始游玩按钮
        binding.btnStartPlaying.setOnClickListener {
            handleStartPlayingClick()
        }

        // 退出登录按钮
        binding.btnLogout.setOnClickListener {
            showLogoutConfirmDialog()
        }

        // 刷新余额按钮
        binding.ivRefreshBalance.setOnClickListener {
            viewModel.refreshBalance()
        }
    }

    /**
     * 设置观察者
     */
    private fun setupObservers() {
        // 观察用户数据变化
        viewModel.userData.observe(this, Observer { userData ->
            updateUserInfo()
        })

        // 观察加载状态
        viewModel.isLoading.observe(this, Observer { isLoading ->
            // 可以在这里显示/隐藏加载指示器
            binding.btnLogout.isEnabled = !isLoading
        })

        // 观察错误信息
        viewModel.errorMessage.observe(this, Observer { error ->
            if (!error.isNullOrEmpty()) {
                // 如果是余额不足的错误，使用自定义的余额不足弹窗
                if (error.contains("余额不足")) {
                    showBalanceWarning()
                } else {
                    MessageDialog.show("错误", error)
                }
                viewModel.clearError()
            }
        })

        // 观察退出登录结果
        viewModel.logoutResult.observe(this, Observer { success ->
            if (success) {
                XLog.d("退出登录成功，关闭页面")
                finish()
            }
        })

        // 观察余额刷新状态
        viewModel.isRefreshingBalance.observe(this, Observer { isRefreshing ->
            // 更新刷新按钮状态
            binding.ivRefreshBalance.isEnabled = !isRefreshing
            binding.ivRefreshBalance.alpha = if (isRefreshing) 0.5f else 1.0f

            // 可以添加旋转动画
            if (isRefreshing) {
                binding.ivRefreshBalance.animate()
                    .rotation(360f)
                    .setDuration(1000)
                    .withEndAction {
                        binding.ivRefreshBalance.rotation = 0f
                    }
            } else {
                // 余额刷新完成后，重新检查余额状态
                checkBalanceStatus()
            }
        })

        loginViewModel.qrCodeBase64.observe(this, Observer { base64 ->
            if (!base64.isNullOrEmpty()) {
                loadQRCodeFromBase64(base64)
            }
        })

        // 观察开始游玩结果
        viewModel.startPlayingResult.observe(this, Observer { success ->
            if (success) {
                moveTaskToBack(true)//退出当前界面
            }
        })
    }

    /**
     * 从Base64数据加载小程序码图片
     */
    private fun loadQRCodeFromBase64(base64: String) {
        try {
            // 解码Base64数据
            val imageBytes = Base64.decode(base64, Base64.DEFAULT)

            // 使用Coil加载图片
            binding.ivQrCode.load(imageBytes) {
                crossfade(true)
                placeholder(R.drawable.ic_launcher_foreground)
                error(R.drawable.ic_launcher_foreground)
            }

            XLog.d("加载小程序码成功，数据长度: ${imageBytes.size}")
        } catch (e: Exception) {
            XLog.e("加载小程序码失败", e)
        }
    }


    /**
     * 更新用户信息显示
     */
    private fun updateUserInfo() {
        val userData = viewModel.userData.value
        if (userData != null) {
            // 设置用户昵称
            binding.tvUserName.text = viewModel.getUserDisplayName()

            // 设置用户余额
            binding.tvUserBalance.text = viewModel.getUserBalanceText()

            // 加载用户头像
            loadUserAvatar(viewModel.getUserAvatarUrl())
        }
    }

    /**
     * 加载用户头像
     * @param avatarUrl 头像URL
     */
    private fun loadUserAvatar(avatarUrl: String?) {
        try {
            // 如果没有头像URL，直接显示默认头像
            if (avatarUrl.isNullOrBlank()) {
                binding.ivUserAvatar.setImageResource(R.drawable.ic_default_avatar)
                binding.pbAvatarLoading.visibility = View.GONE
                return
            }

            binding.ivUserAvatar.load(avatarUrl) {
                crossfade(true)
                placeholder(R.drawable.ic_default_avatar)
                error(R.drawable.ic_default_avatar)
                transformations(CircleCropTransformation())
                listener(
                    onStart = {
                        XLog.d("开始加载用户头像: $avatarUrl")
                        binding.pbAvatarLoading.visibility = View.VISIBLE
                    },
                    onError = { _, throwable ->
                        XLog.w("加载用户头像失败: $avatarUrl")
                        binding.pbAvatarLoading.visibility = View.GONE
                    },
                    onSuccess = { _, _ ->
                        XLog.d("用户头像加载成功: $avatarUrl")
                        binding.pbAvatarLoading.visibility = View.GONE
                    }
                )
            }
        } catch (e: Exception) {
            XLog.e("设置用户头像失败", e)
            // 如果出现异常，设置默认头像并隐藏加载指示器
            binding.ivUserAvatar.setImageResource(R.drawable.ic_default_avatar)
            binding.pbAvatarLoading.visibility = View.GONE
        }
    }

    /**
     * 显示退出登录确认对话框
     */
    private fun showLogoutConfirmDialog() {
        MessageDialog.show("退出登录", "确定要退出登录吗？\n\n⚠️ 注意：退出后将清理以下数据：\n• 登录状态\n• 用户信息\n• 应用缓存\n• 所有本地数据")
            .setOkButton("确定退出") { _, _ ->
                // 直接启动AppDataCleanupActivity进行清理
                val intent = Intent(this@UserInfoActivity, AppDataCleanupActivity::class.java)
                startActivity(intent)
                // 退出登录后都应该finish，不需要根据入口类型处理
                finish()
                false
            }
            .setCancelButton("取消")
    }

    /**
     * 处理开始游玩按钮点击
     */
    private fun handleStartPlayingClick() {
        try {
            XLog.d("开始游玩按钮点击，入口类型: $entryType")
            // 先刷新余额和倒计时，然后检查是否足够
            viewModel.startPlayingWithRefresh()
        } catch (e: Exception) {
            XLog.e("开始游玩时发生错误", e)
            MessageDialog.show("错误", "开始游玩时发生错误，请稍后重试")
                .setOkButton("确定")
        }
    }

    /**
     * 处理返回键
     */
    override fun onBackPressed() {
        when (entryType) {
            UserInfoEntryType.NORMAL_ENTRY -> {
                // 正常入口屏蔽返回键
                XLog.d("正常入口：返回键被屏蔽")
            }
            UserInfoEntryType.COUNTDOWN_ENDED_ENTRY -> {
                // 倒计时入口：返回键复用继续按钮的逻辑
                XLog.d("倒计时入口：返回键触发继续逻辑")
                handleStartPlayingClick()
            }
        }
    }

    /**
     * 暂停倒计时和扣款
     */
    private fun pauseCountdownAndDeduction() {
        try {
            CountdownService.pauseCountdownStatic()
            XLog.d("用户信息页面：暂停倒计时和扣款")
        } catch (e: Exception) {
            XLog.e("暂停倒计时失败", e)
        }
    }

    /**
     * 恢复倒计时和扣款
     */
    private fun resumeCountdownAndDeduction() {
        try {
            CountdownService.resumeCountdownStatic()
            XLog.d("用户信息页面：恢复倒计时和扣款")
        } catch (e: Exception) {
            XLog.e("恢复倒计时失败", e)
        }
    }


    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        // 当Activity被复用时，更新Intent并重新初始化
        setIntent(intent)
        XLog.d("UserInfoActivity被复用，重新初始化")
        viewModel.resetState()

        initData(intent)
    }

    override fun onPause() {
        super.onPause()
        // 当Activity失去焦点时恢复全局倒计时，停止页面倒计时
        // 这样无论是moveTaskToBack还是其他方式离开页面都会恢复全局倒计时
        resumeCountdownAndDeduction()
        // 停止页面倒计时，避免Activity复用时的竞态条件
        stopPageCountdown()
        XLog.d("用户信息页面失去焦点，恢复全局倒计时，停止页面倒计时")
    }

    override fun onResume() {
        super.onResume()
        // 当Activity重新获得焦点时暂停全局倒计时，重新启动页面倒计时
        pauseCountdownAndDeduction()

        XLog.d("用户信息页面获得焦点，暂停全局倒计时，启动页面倒计时")
    }

    override fun onDestroy() {
        super.onDestroy()
        // 停止页面倒计时
        stopPageCountdown()
        XLog.d("用户信息页面销毁")
    }

    /**
     * 检查余额状态（仅用于初始化时的静默检查）
     */
    private fun checkBalanceStatus() {
        try {
            // 检查余额是否足够（仅记录日志，不弹窗）
            val isBalanceSufficient = BalancePollingManager.isBalanceSufficientForMinutes(1.0)

            if (!isBalanceSufficient) {
                XLog.d("余额不足（静默检查）")
            } else {
                XLog.d("余额充足（静默检查）")
            }
        } catch (e: Exception) {
            XLog.e("检查余额状态失败", e)
        }
    }

    /**
     * 显示余额不足警告Dialog（使用RentPad设计图样式）
     */
    private fun showBalanceWarning() {
        try {
            val (currentBalance, perMinuteCost) = viewModel.getCurrentBalanceInfo()
            val message = if (currentBalance != null && perMinuteCost != null) {
                "当前余额：${String.format("%.2f", currentBalance)}元\n" +
                        "每分钟需要：${String.format("%.2f", perMinuteCost)}元\n\n请及时充值后继续使用"
            } else {
                "余额不足，无法继续使用\n\n请及时充值后继续使用"
            }

            // 使用自定义DialogX弹窗
            val dialog = CustomDialog.build()
                .setCustomView(object : OnBindView<CustomDialog>(R.layout.dialog_balance_insufficient){
                    override fun onBind(
                        dialog: CustomDialog,
                        v: View
                    ) {
                        // 设置消息文本
                        val tvMessage = v.findViewById<TextView>(R.id.tv_balance_message)
                        tvMessage.text = message

                        // 设置确认按钮点击事件
                        val btnConfirm = v.findViewById<Button>(R.id.btn_confirm)
                        btnConfirm.setOnClickListener {
                            dialog.dismiss()
                        }
                    }

                })
                .setCancelable(true)
                .show()

            // 3秒后自动关闭Dialog
            Handler(Looper.getMainLooper()).postDelayed({
                try {
                    dialog.dismiss()
                } catch (e: Exception) {
                    XLog.w("自动关闭余额不足Dialog失败", e)
                }
            }, 3000)

            XLog.d("显示余额不足Dialog: $message")
        } catch (e: Exception) {
            XLog.e("显示余额不足Dialog失败", e)
        }
    }

    /**
     * 隐藏余额不足警告（Dialog形式不需要手动隐藏）
     */
    private fun hideBalanceWarning() {
        // Dialog形式不需要手动隐藏，保留方法以保持兼容性
        XLog.d("余额充足，无需显示警告")
    }

    /**
     * 显示倒计时（顶部倒计时始终显示）
     */
    private fun showCountdown() {
        // 顶部倒计时始终显示，无需额外操作
    }

    /**
     * 隐藏倒计时（顶部倒计时始终显示）
     */
    private fun hideCountdown() {
        // 顶部倒计时始终显示，无需额外操作
    }

    /**
     * 启动页面倒计时（独立于余额状态）
     */
    private fun startPageCountdown() {
        try {
            if (isCountdownActive) {
                XLog.d("倒计时已在运行，跳过启动")
                return
            }

            XLog.d("启动$PAGE_COUNTDOWN_MINUTES 分钟倒计时（独立于余额状态）")
            isCountdownActive = true

            pageCountdown = object : CountDownTimer(PAGE_COUNTDOWN_MINUTES * 60 * 1000L, 1000L) {
                override fun onTick(millisUntilFinished: Long) {
                    val minutes = millisUntilFinished / 60000
                    val seconds = (millisUntilFinished % 60000) / 1000
                    val timeText = String.format("%02d:%02d", minutes, seconds)
                    binding.tvCountdownTime.text = timeText
                }

                override fun onFinish() {
                    XLog.d("$PAGE_COUNTDOWN_MINUTES 分钟倒计时结束，启动退出登录流程")
                    isCountdownActive = false
                    startLogoutProcess()
                }
            }.start()

        } catch (e: Exception) {
            XLog.e("启动$PAGE_COUNTDOWN_MINUTES 分钟倒计时失败", e)
            isCountdownActive = false
        }
    }

    /**
     * 停止页面倒计时
     */
    private fun stopPageCountdown() {
        try {
            pageCountdown?.cancel()
            pageCountdown = null
            isCountdownActive = false
            XLog.d("停止${PAGE_COUNTDOWN_MINUTES}分钟页面倒计时")
        } catch (e: Exception) {
            XLog.e("停止${PAGE_COUNTDOWN_MINUTES}分钟页面倒计时失败", e)
        }
    }

    /**
     * 启动退出登录流程
     */
    private fun startLogoutProcess() {
        try {
            XLog.d("${PAGE_COUNTDOWN_MINUTES}分钟页面倒计时结束，启动完整退出登录流程")

            // 启动AppDataCleanupActivity进行完整的数据清理和退出登录
            val intent = Intent(this@UserInfoActivity, AppDataCleanupActivity::class.java)
            startActivity(intent)

            // 退出当前页面
            finish()

        } catch (e: Exception) {
            XLog.e("启动退出登录流程失败", e)
            // 异常情况下直接finish
            finish()
        }
    }
}
