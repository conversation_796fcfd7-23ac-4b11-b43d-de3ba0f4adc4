package com.hwb.timecontroller.activity

import android.os.Bundle
import android.view.View
import android.view.WindowManager
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.hwb.timecontroller.R
import com.hwb.timecontroller.business.AdminManager
import com.hwb.timecontroller.viewModel.AppDataCleanupViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import com.elvishew.xlog.XLog

/**
 * 应用数据清理Activity
 * 全屏Loading样式的Activity，在倒计时结束后自动弹出显示
 * <p>
 * Author:huangwubin
 * Contacts:<EMAIL>
 * <p>
 * changeLogs:
 * 2025/7/8: First created this class.
 */
class AppDataCleanupActivity : AppCompatActivity() {

    // ViewModel
    private val cleanupViewModel by viewModels<AppDataCleanupViewModel>()

    // UI组件
    private lateinit var ivLoadingAnimation: android.widget.ImageView
    private lateinit var tvStatusText: android.widget.TextView
    private lateinit var layoutProgressContainer: android.widget.LinearLayout
    private lateinit var progressCleanup: android.widget.ProgressBar
    private lateinit var tvProgressPercent: android.widget.TextView
    private lateinit var layoutCompletionHint: android.widget.LinearLayout
    private lateinit var ivCompletionHint: android.widget.ImageView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        XLog.d("AppDataCleanupActivity启动")
        
        setContentView(R.layout.activity_app_data_cleanup)

        // 设置全屏和半透明背景
        setupFullscreenAndTransparent()
        
        // 隐藏ActionBar
        supportActionBar?.hide()

        // 初始化UI组件
        initViews()
        
        // 设置观察者
        setupObservers()
        
        // 启动清理流程
        cleanupViewModel.startCleanupProcess()
    }

    /**
     * 设置全屏和半透明背景
     */
    private fun setupFullscreenAndTransparent() {
        try {
            // 设置全屏标志
            window.setFlags(
                WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN
            )
            
            // 设置保持屏幕常亮
            window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
            
            // 隐藏系统UI
            window.decorView.systemUiVisibility = (
                View.SYSTEM_UI_FLAG_FULLSCREEN or
                View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or
                View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
            )
            
        } catch (e: Exception) {
            XLog.e("设置全屏模式失败", e)
        }
    }

    /**
     * 初始化UI组件
     */
    private fun initViews() {
        try {
            ivLoadingAnimation = findViewById(R.id.iv_loading_animation)
            tvStatusText = findViewById(R.id.tv_status_text)
            layoutProgressContainer = findViewById(R.id.layout_progress_container)
            progressCleanup = findViewById(R.id.progress_cleanup)
            tvProgressPercent = findViewById(R.id.tv_progress_percent)
            layoutCompletionHint = findViewById(R.id.layout_completion_hint)
            ivCompletionHint = findViewById(R.id.iv_completion_hint)

            // 启动loading动画
            startLoadingAnimation()

        } catch (e: Exception) {
            XLog.e("初始化UI组件失败", e)
        }
    }

    /**
     * 设置观察者
     */
    private fun setupObservers() {
        try {
            // 观察清理状态
            cleanupViewModel.cleanupState.observe(this) { state ->
                handleCleanupStateChange(state)
            }

            // 观察状态文本
            cleanupViewModel.statusText.observe(this) { text ->
                tvStatusText.text = text
            }

            // 观察清理进度
            cleanupViewModel.cleanupProgress.observe(this) { progress ->
                updateProgress(progress)
            }

            // 观察清理完成
            cleanupViewModel.cleanupCompleted.observe(this) { completed ->
                if (completed) {
                    handleCleanupCompleted()
                }
            }

        } catch (e: Exception) {
            XLog.e("设置观察者失败", e)
        }
    }

    /**
     * 处理清理状态变化
     */
    private fun handleCleanupStateChange(state: AppDataCleanupViewModel.CleanupState) {
        try {
            when (state) {
                AppDataCleanupViewModel.CleanupState.PREPARING -> {
                    // 准备阶段，显示Loading
                }
                
                AppDataCleanupViewModel.CleanupState.BUSINESS_CHECK -> {
                    // 业务判断阶段
                }
                
                AppDataCleanupViewModel.CleanupState.CLEANING -> {
                    // 清理阶段，显示完成提示
                    layoutCompletionHint.visibility = View.VISIBLE
                }
                
                AppDataCleanupViewModel.CleanupState.COMPLETED -> {
                    // 清理完成
                    XLog.d("应用数据清理流程完成")
                }
                
                AppDataCleanupViewModel.CleanupState.ERROR -> {
                    // 清理出错
                    XLog.e("应用数据清理流程出错")
                }
            }
        } catch (e: Exception) {
            XLog.e("处理清理状态变化失败", e)
        }
    }

    /**
     * 处理清理完成
     */
    private fun handleCleanupCompleted() {
        try {
            XLog.d("应用数据清理完成，准备重启应用")
            
            // 延迟2秒后重启应用，让用户看到完成状态
            lifecycleScope.launch {
                delay(2000)
                
                // 重启应用
                AdminManager.restartApp()
                
                // 结束当前Activity
                finish()
            }
            
        } catch (e: Exception) {
            XLog.e("处理清理完成失败", e)
        }
    }

    /**
     * 禁用返回键
     */
    override fun onBackPressed() {
        // 清理过程中禁用返回键
        XLog.d("清理过程中禁用返回键")
    }

    /**
     * 启动loading动画
     */
    private fun startLoadingAnimation() {
        try {
            val rotateAnimation = android.view.animation.RotateAnimation(
                0f, 360f,
                android.view.animation.Animation.RELATIVE_TO_SELF, 0.5f,
                android.view.animation.Animation.RELATIVE_TO_SELF, 0.5f
            ).apply {
                duration = 2000
                repeatCount = android.view.animation.Animation.INFINITE
                interpolator = android.view.animation.LinearInterpolator()
            }
            ivLoadingAnimation.startAnimation(rotateAnimation)
        } catch (e: Exception) {
            XLog.e("启动loading动画失败", e)
        }
    }

    /**
     * 更新清理进度
     */
    private fun updateProgress(progress: Int) {
        try {
            progressCleanup.progress = progress
            tvProgressPercent.text = "${progress}%"

            // 显示进度容器
            if (layoutProgressContainer.visibility != View.VISIBLE) {
                layoutProgressContainer.visibility = View.VISIBLE
            }

            XLog.d("更新清理进度: ${progress}%")
        } catch (e: Exception) {
            XLog.e("更新清理进度失败", e)
        }
    }

    override fun onDestroy() {
        try {
            XLog.d("AppDataCleanupActivity销毁")
            super.onDestroy()
        } catch (e: Exception) {
            XLog.e("AppDataCleanupActivity销毁失败", e)
        }
    }
}
