<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center">

    <!-- 弹窗背景 -->
    <ImageView
        android:layout_width="400dp"
        android:layout_height="300dp"
        android:adjustViewBounds="true"
        android:scaleType="fitXY"
        android:src="@drawable/rentpad_exit_dialog" />

    <!-- 弹窗内容 -->
    <LinearLayout
        android:layout_width="400dp"
        android:layout_height="300dp"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="40dp">

        <!-- 余额不足图标 -->
        <ImageView
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginBottom="20dp"
            android:src="@drawable/rentpad_balance_insufficient" />

        <!-- 提示文字 -->
        <TextView
            android:id="@+id/tv_balance_message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="30dp"
            android:gravity="center"
            android:text="余额不足，无法继续使用\n\n请及时充值后继续使用"
            android:textColor="@color/text_primary"
            android:textSize="18sp"
            android:textStyle="bold" />

        <!-- 按钮区域 -->
        <FrameLayout
            android:layout_width="120dp"
            android:layout_height="wrap_content">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                android:scaleType="fitXY"
                android:src="@drawable/rentpad_confirm_exit" />

            <Button
                android:id="@+id/btn_confirm"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:background="@android:color/transparent"
                android:gravity="center"
                android:text="知道了"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:textStyle="bold" />

        </FrameLayout>

    </LinearLayout>

</FrameLayout>
