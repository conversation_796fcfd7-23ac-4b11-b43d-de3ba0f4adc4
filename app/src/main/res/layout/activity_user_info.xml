<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- 背景图片 -->
    <ImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:src="@drawable/rentpad_background" />

    <!-- 主要内容 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="24dp">

        <!-- 顶部倒计时框架 -->
        <FrameLayout
            android:layout_width="370dp"
            android:layout_height="60dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="32dp"
            android:background="@drawable/rentpad_top_frame">

            <TextView
                android:id="@+id/tv_countdown_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="120dp"
                android:gravity="center"
                android:text="03:00"
                android:textColor="@color/white"
                android:textSize="24sp"
                android:textStyle="bold" />

            <!-- 返回按钮（隐藏） -->
            <ImageButton
                android:id="@+id/btn_back"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_gravity="start|center_vertical"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="返回"
                android:src="@drawable/ic_arrow_back"
                android:tint="@color/white"
                android:visibility="gone" />

        </FrameLayout>

        <!-- 用户信息区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="40dp"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="32dp">

            <!-- 左侧用户信息 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <!-- 用户头像 -->
                <FrameLayout
                    android:layout_width="150dp"
                    android:layout_height="150dp"
                    android:layout_marginBottom="20dp">

                    <ImageView
                        android:id="@+id/iv_user_avatar"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/circle_background"
                        android:contentDescription="用户头像"
                        android:scaleType="centerCrop"
                        android:src="@drawable/rentpad_avatar" />

                    <!-- 头像加载指示器 -->
                    <ProgressBar
                        android:id="@+id/pb_avatar_loading"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_gravity="center"
                        android:indeterminateTint="@color/lock_accent_gold"
                        android:visibility="gone" />

                </FrameLayout>

                <!-- 用户昵称 -->
                <TextView
                    android:id="@+id/tv_user_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="20dp"
                    android:text="用户昵称"
                    android:textColor="@color/white"
                    android:textSize="28sp"
                    android:textStyle="bold" />

                <!-- 用户余额 -->
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/balance_background"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="28dp"
                        android:layout_height="28dp"
                        android:layout_marginEnd="8dp"
                        android:src="@drawable/rentpad_coin" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="余额："
                        android:textColor="@color/text_secondary"
                        android:textSize="18sp" />

                    <TextView
                        android:id="@+id/tv_user_balance"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:textColor="@color/lock_accent_gold"
                        android:textSize="22sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text=" 元"
                        android:textColor="@color/text_secondary"
                        android:textSize="18sp" />

                    <!-- 刷新按钮 -->
                    <ImageView
                        android:id="@+id/iv_refresh_balance"
                        android:layout_width="28dp"
                        android:layout_height="28dp"
                        android:layout_marginStart="12dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:contentDescription="刷新余额"
                        android:padding="4dp"
                        android:src="@drawable/rentpad_refresh" />

                </LinearLayout>
            </LinearLayout>

            <!-- 右侧二维码 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <FrameLayout
                    android:layout_width="220dp"
                    android:layout_height="220dp"
                    android:layout_gravity="center"
                    android:background="@drawable/qr_card_background"
                    android:padding="16dp">

                    <ImageView
                        android:id="@+id/iv_qr_code"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:scaleType="fitCenter" />

                </FrameLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="12dp"
                    android:text="扫码充值后请手动刷新余额"
                    android:textColor="@color/white"
                    android:textSize="16sp" />
            </LinearLayout>

        </LinearLayout>

        <!-- 占位空间 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <!-- 开始租借平板按钮 -->
        <FrameLayout
            android:layout_width="300dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="16dp">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                android:scaleType="fitXY"
                android:src="@drawable/rentpad_start_rental" />

            <Button
                android:id="@+id/btn_start_playing"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:background="@android:color/transparent"
                android:gravity="center"
                android:text="开始租借平板"
                android:textColor="@color/white"
                android:textSize="20sp"
                android:textStyle="bold" />

        </FrameLayout>

        <!-- 退出登录按钮 -->
        <FrameLayout
            android:layout_width="300dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="16dp">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                android:scaleType="fitXY"
                android:src="@drawable/rentpad_logout" />

            <Button
                android:id="@+id/btn_logout"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:background="@android:color/transparent"
                android:gravity="center"
                android:text="退出登录"
                android:textColor="@color/white"
                android:textSize="20sp"
                android:textStyle="bold" />

        </FrameLayout>

    </LinearLayout>

</FrameLayout>
